import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/screens/profile_screen.dart';
import 'package:gameflex_mobile/screens/upload_screen.dart';
import 'package:gameflex_mobile/providers/posts_provider.dart';
import 'package:gameflex_mobile/providers/auth_provider.dart';
import 'package:gameflex_mobile/providers/channels_provider.dart';
import 'package:gameflex_mobile/utils/app_logger.dart';
import 'package:gameflex_mobile/widgets/feed.dart';
import 'package:gameflex_mobile/widgets/custom_bottom_navigation.dart';
import 'package:gameflex_mobile/widgets/home_tab_with_navigation.dart';
import 'package:gameflex_mobile/screens/reflexes_feed_screen.dart';
import 'package:gameflex_mobile/main.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with RouteAware {
  int _selectedIndex = 0;
  int _feedTabIndex =
      1; // 0 for Followed, 1 for Prime (default), 2 for Channels
  late final Widget _feedWidget;

  @override
  void initState() {
    super.initState();
    // Initialize the feed widget once to preserve state
    _feedWidget = const Feed();

    // Load posts when the home screen is first created, but only if authenticated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);

      // Only load posts if user is authenticated
      if (authProvider.isAuthenticated &&
          postsProvider.status == PostsStatus.initial) {
        postsProvider.loadPosts();
      }
      // Start real-time subscriptions
      postsProvider.startRealtimeSubscriptions();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      MyApp.routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    MyApp.routeObserver.unsubscribe(this);
    super.dispose();
  }

  // RouteAware methods
  @override
  void didPopNext() {
    // User came back to this screen from another screen
    // Refresh the feed if we're on the home tab
    AppLogger.navigation('User returned from another screen');
    if (_selectedIndex == 0) {
      AppLogger.navigation('On home tab - refreshing feed');
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      postsProvider.refreshPosts();
    }
  }

  @override
  void didPush() {}

  @override
  void didPop() {}

  @override
  void didPushNext() {}

  void _onItemTapped(int index) {
    // If tapping home button while already on home, refresh the feed
    if (index == 0 && _selectedIndex == 0) {
      AppLogger.navigation(
        'Home button tapped while on home - refreshing feed',
      );
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      postsProvider.refreshPosts();
      return;
    }

    // If navigating back to home from another tab, refresh the feed
    if (index == 0 && _selectedIndex != 0) {
      AppLogger.navigation(
        'Navigating back to home from tab $_selectedIndex - refreshing feed',
      );
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      postsProvider.refreshPosts();
    }

    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          _selectedIndex == 0
              ? null
              : AppBar(
                title: Text(
                  _getTabTitle(),
                  style: const TextStyle(
                    color: AppColors.gfGreen,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.search, color: AppColors.gfOffWhite),
                    onPressed: () {},
                  ),
                ],
              ),
      body: Stack(
        children: [
          _buildBody(),
          // Reflex navigation button (only show on home tab)
          if (_selectedIndex == 0) _buildReflexNavigationButton(),
        ],
      ),
      bottomNavigationBar: CustomBottomNavigation(
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
      ),
    );
  }

  String _getTabTitle() {
    switch (_selectedIndex) {
      case 1:
        return 'Upload';
      case 2:
        return 'Profile';
      default:
        return 'GameFlex';
    }
  }

  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeTab();
      case 1:
        return const UploadScreen();
      case 2:
        return const ProfileScreen();
      default:
        return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    return Column(
      children: [
        SafeArea(bottom: false, child: _buildFeedTabs()),
        Expanded(child: _buildFeedContent()),
      ],
    );
  }

  Widget _buildFeedTabs() {
    return Container(
      height: 50,
      decoration: const BoxDecoration(
        color: AppColors.gfDarkBlue,
        border: Border(
          bottom: BorderSide(color: AppColors.gfGrayBorder, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildTabButton(
              title: 'Followed',
              isSelected: _feedTabIndex == 0,
              onTap: () => _onFeedTabTapped(0),
            ),
          ),
          Container(width: 1, height: 20, color: AppColors.gfGrayBorder),
          Expanded(
            child: _buildTabButton(
              title: 'Prime',
              isSelected: _feedTabIndex == 1,
              onTap: () => _onFeedTabTapped(1),
            ),
          ),
          Container(width: 1, height: 20, color: AppColors.gfGrayBorder),
          Expanded(
            child: _buildTabButton(
              title: 'Channels',
              isSelected: _feedTabIndex == 2,
              onTap: () => _onFeedTabTapped(2),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton({
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          color:
              isSelected
                  ? AppColors.gfGreen.withValues(alpha: 0.1)
                  : Colors.transparent,
          border: Border(
            bottom: BorderSide(
              color: isSelected ? AppColors.gfGreen : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              color: isSelected ? AppColors.gfGreen : AppColors.gfGrayText,
              fontSize: 16,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeedContent() {
    switch (_feedTabIndex) {
      case 0:
        // Followed tab - could show posts from followed users
        return _feedWidget;
      case 1:
        // Prime tab - default feed
        return _feedWidget;
      case 2:
        // Channels tab - show channels
        return const ChannelsScreenContent();
      default:
        return _feedWidget;
    }
  }

  void _onFeedTabTapped(int index) {
    setState(() {
      _feedTabIndex = index;
    });

    // Load channels when switching to channels tab
    if (index == 2) {
      final channelsProvider = Provider.of<ChannelsProvider>(
        context,
        listen: false,
      );
      if (channelsProvider.channels.isEmpty &&
          channelsProvider.status == ChannelsStatus.initial) {
        channelsProvider.loadChannels();
      }
    }
  }

  Widget _buildReflexNavigationButton() {
    return Positioned(
      right: 0, // Align to the very right edge
      top: MediaQuery.of(context).size.height * 0.4, // Middle right of screen
      child: GestureDetector(
        onTap: _navigateToReflexes,
        onPanStart: (details) {
          // Store initial position for swipe detection
        },
        onPanEnd: (details) {
          // Handle swipe left gesture based on velocity
          if (details.velocity.pixelsPerSecond.dx < -500) {
            _navigateToReflexes();
          }
        },
        child: Container(
          width: 40,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.gfGreen.withValues(alpha: 0.9),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              bottomLeft: Radius.circular(20),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(-2, 0),
              ),
            ],
          ),
          child: const Center(
            child: Icon(
              Icons.keyboard_arrow_left,
              color: Colors.black,
              size: 24,
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToReflexes() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const ReflexesFeedScreen()));
  }
}
