import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/reflex_model.dart';
import '../providers/reflex_provider.dart';
import '../providers/posts_provider.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../widgets/reflex_item.dart';

class ReflexesFeedScreen extends StatefulWidget {
  const ReflexesFeedScreen({super.key});

  @override
  State<ReflexesFeedScreen> createState() => _ReflexesFeedScreenState();
}

class _ReflexesFeedScreenState extends State<ReflexesFeedScreen> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;
  List<ReflexModel> _allReflexes = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadReflexesFromPosts();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadReflexesFromPosts() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      AppLogger.debug('ReflexesFeedScreen: Loading reflexes from posts');

      // Get posts first
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      if (postsProvider.posts.isEmpty) {
        await postsProvider.loadPosts();
      }

      // Get reflexes for each post that has reflexes
      if (!mounted) return;
      final reflexProvider = Provider.of<ReflexProvider>(
        context,
        listen: false,
      );
      final allReflexes = <ReflexModel>[];

      for (final post in postsProvider.posts) {
        if (post.reflexCount > 0) {
          await reflexProvider.loadReflexesForPost(post.id);
          final postReflexes = reflexProvider.getReflexesForPost(post.id);
          allReflexes.addAll(postReflexes);
        }
      }

      // Sort by creation time (most recent first)
      allReflexes.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      setState(() {
        _allReflexes = allReflexes;
        _isLoading = false;
      });

      AppLogger.debug(
        'ReflexesFeedScreen: Loaded ${allReflexes.length} total reflexes',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexesFeedScreen: Error loading reflexes',
        error: e,
        stackTrace: stackTrace,
      );
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshReflexes() async {
    setState(() {
      _allReflexes.clear();
    });
    await _loadReflexesFromPosts();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'Reflexes',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: AppColors.gfOffWhite),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: AppColors.gfOffWhite),
            onPressed: _refreshReflexes,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading && _allReflexes.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
        ),
      );
    }

    if (_error != null && _allReflexes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            const Text(
              'Error loading reflexes',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error ?? 'Unknown error',
              style: const TextStyle(color: AppColors.gfGrayText, fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _refreshReflexes,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.gfGreen,
                foregroundColor: Colors.black,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_allReflexes.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.sports_martial_arts,
              color: AppColors.gfGrayText,
              size: 64,
            ),
            SizedBox(height: 16),
            Text(
              'No reflexes yet',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Reflexes will appear here when users create them',
              style: TextStyle(color: AppColors.gfGrayText, fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshReflexes,
      color: AppColors.gfGreen,
      backgroundColor: AppColors.gfDarkBackground,
      child: PageView.builder(
        controller: _pageController,
        scrollDirection: Axis.vertical,
        itemCount: _allReflexes.length,
        onPageChanged: (index) {
          AppLogger.debug('ReflexesFeedScreen: Page changed to index $index');
          setState(() {
            _currentIndex = index;
          });
        },
        itemBuilder: (context, index) {
          if (index >= _allReflexes.length) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
              ),
            );
          }

          final reflex = _allReflexes[index];
          return ReflexItem(reflex: reflex, isVisible: index == _currentIndex);
        },
      ),
    );
  }
}
